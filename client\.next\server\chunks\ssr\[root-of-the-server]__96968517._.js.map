{"version": 3, "sources": [], "sections": [{"offset": {"line": 3, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 11, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Internship-Projects/The-Salty-Devs/client/src/lib/api.ts"], "sourcesContent": ["import { Post } from '@/types';\r\n\r\n// The base URL of your backend API\r\nconst API_BASE_URL =\r\n  process.env.NEXT_PUBLIC_API_URL || 'http://localhost:4000/api';\r\n\r\n/**\r\n * Fetches all published posts from the backend.\r\n */\r\nexport async function getAllPosts(): Promise<{\r\n  posts: Post[];\r\n  totalCount: number;\r\n}> {\r\n  try {\r\n    const response = await fetch(`${API_BASE_URL}/posts`, {\r\n      // Improve performance by re-fetching data every 60 seconds\r\n      next: { revalidate: 60 },\r\n    });\r\n\r\n    if (!response.ok) {\r\n      throw new Error('Failed to fetch posts');\r\n    }\r\n\r\n    const responseData = await response.json();\r\n    return {\r\n      posts: responseData.data, // Extract the 'data' array\r\n      totalCount: responseData.totalCount,\r\n    };\r\n  } catch (error) {\r\n    console.error('Error fetching posts:', error);\r\n    // In a real app, you'd handle this error more gracefully\r\n    return { posts: [], totalCount: 0 }; // Return an empty array on error\r\n  }\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA,mCAAmC;AACnC,MAAM,eACJ,QAAQ,GAAG,CAAC,mBAAmB,IAAI;AAK9B,eAAe;IAIpB,IAAI;QACF,MAAM,WAAW,MAAM,MAAM,GAAG,aAAa,MAAM,CAAC,EAAE;YACpD,2DAA2D;YAC3D,MAAM;gBAAE,YAAY;YAAG;QACzB;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM;QAClB;QAEA,MAAM,eAAe,MAAM,SAAS,IAAI;QACxC,OAAO;YACL,OAAO,aAAa,IAAI;YACxB,YAAY,aAAa,UAAU;QACrC;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,yBAAyB;QACvC,yDAAyD;QACzD,OAAO;YAAE,OAAO,EAAE;YAAE,YAAY;QAAE,GAAG,iCAAiC;IACxE;AACF", "debugId": null}}, {"offset": {"line": 46, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Internship-Projects/The-Salty-Devs/client/src/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst badgeVariants = cva(\n  \"inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90\",\n        secondary:\n          \"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90\",\n        destructive:\n          \"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  }\n)\n\nfunction Badge({\n  className,\n  variant,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"span\"> &\n  VariantProps<typeof badgeVariants> & { asChild?: boolean }) {\n  const Comp = asChild ? Slot : \"span\"\n\n  return (\n    <Comp\n      data-slot=\"badge\"\n      className={cn(badgeVariants({ variant }), className)}\n      {...props}\n    />\n  )\n}\n\nexport { Badge, badgeVariants }\n"], "names": [], "mappings": ";;;;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,gBAAgB,IAAA,iLAAG,EACvB,kZACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,aACE;YACF,SACE;QACJ;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAGF,SAAS,MAAM,EACb,SAAS,EACT,OAAO,EACP,UAAU,KAAK,EACf,GAAG,OAEuD;IAC1D,MAAM,OAAO,UAAU,kLAAI,GAAG;IAE9B,qBACE,wPAAC;QACC,aAAU;QACV,WAAW,IAAA,mIAAE,EAAC,cAAc;YAAE;QAAQ,IAAI;QACzC,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 92, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Internship-Projects/The-Salty-Devs/client/src/components/postCard/index.tsx"], "sourcesContent": ["import React from 'react';\r\nimport Link from 'next/link';\r\nimport { Eye, User } from 'lucide-react';\r\nimport { Badge } from '@/components/ui/badge';\r\nimport { Post } from '@/types';\r\n\r\ninterface PostCardProps {\r\n  post: Post;\r\n}\r\n\r\nconst PostCard = ({ post }: PostCardProps) => {\r\n  return (\r\n    <Link href={`/articles/${post.slug}`}>\r\n      {' '}\r\n      {/* TODO: Change to post.slug */}\r\n      <article className=\"group bg-card border border-border p-6 hover:gradient-border transition-all duration-300 h-full flex flex-col\">\r\n        <div className=\"space-y-4 flex-1\">\r\n          {/* Category and View Count*/}\r\n          <div className=\"flex items-center justify-between\">\r\n            {/* <Badge variant=\"secondary\" className=\"text-xs\">\r\n              {post.category}\r\n            </Badge> */}\r\n            {post.views > 0 && (\r\n              <div className=\"flex items-center space-x-1 text-xs text-muted-foreground\">\r\n                <Eye className=\"h-3 w-3\" />\r\n                <span>{post.views.toLocaleString()}</span>\r\n              </div>\r\n            )}\r\n          </div>\r\n\r\n          {/* Title */}\r\n          <h2 className=\"text-xl font-bold text-card-foreground group-hover:gradient-text transition-colors line-clamp-2\">\r\n            {post.title}\r\n          </h2>\r\n\r\n          {/* Excerpt */}\r\n          <p className=\"text-muted-foreground leading-relaxed line-clamp-3 flex-1\">\r\n            {post.content}\r\n          </p>\r\n        </div>\r\n\r\n        {/* Footer */}\r\n        <div className=\"mt-4 pt-4 border-t border-border space-y-3\">\r\n          {/* Author and Date */}\r\n          <div className=\"flex items-center justify-between text-sm\">\r\n            <div className=\"flex items-center space-x-2 text-secondary-foreground\">\r\n              <User className=\"h-3 w-3\" />\r\n              <span>{post.author.name}</span>\r\n            </div>\r\n            <time className=\"text-secondary-foreground font-mono\">\r\n              {post.publishedAt}\r\n            </time>\r\n          </div>\r\n\r\n          {/* Tags */}\r\n          {post.tags?.length > 0 && (\r\n            <div className=\"flex flex-wrap gap-2\">\r\n              {post.tags.slice(0, 3).map((tag) => (\r\n                <Badge key={tag} variant=\"outline\" className=\"text-xs\">\r\n                  {tag}\r\n                </Badge>\r\n              ))}\r\n              {post.tags.length > 3 && (\r\n                <Badge variant=\"outline\" className=\"text-xs\">\r\n                  +{post.tags.length - 3}\r\n                </Badge>\r\n              )}\r\n            </div>\r\n          )}\r\n        </div>\r\n      </article>\r\n    </Link>\r\n  );\r\n};\r\n\r\nexport default PostCard;\r\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAAA;AACA;;;;;AAOA,MAAM,WAAW,CAAC,EAAE,IAAI,EAAiB;IACvC,qBACE,wPAAC,iLAAI;QAAC,MAAM,CAAC,UAAU,EAAE,KAAK,IAAI,EAAE;;YACjC;0BAED,wPAAC;gBAAQ,WAAU;;kCACjB,wPAAC;wBAAI,WAAU;;0CAEb,wPAAC;gCAAI,WAAU;0CAIZ,KAAK,KAAK,GAAG,mBACZ,wPAAC;oCAAI,WAAU;;sDACb,wPAAC,iNAAG;4CAAC,WAAU;;;;;;sDACf,wPAAC;sDAAM,KAAK,KAAK,CAAC,cAAc;;;;;;;;;;;;;;;;;0CAMtC,wPAAC;gCAAG,WAAU;0CACX,KAAK,KAAK;;;;;;0CAIb,wPAAC;gCAAE,WAAU;0CACV,KAAK,OAAO;;;;;;;;;;;;kCAKjB,wPAAC;wBAAI,WAAU;;0CAEb,wPAAC;gCAAI,WAAU;;kDACb,wPAAC;wCAAI,WAAU;;0DACb,wPAAC,oNAAI;gDAAC,WAAU;;;;;;0DAChB,wPAAC;0DAAM,KAAK,MAAM,CAAC,IAAI;;;;;;;;;;;;kDAEzB,wPAAC;wCAAK,WAAU;kDACb,KAAK,WAAW;;;;;;;;;;;;4BAKpB,KAAK,IAAI,EAAE,SAAS,mBACnB,wPAAC;gCAAI,WAAU;;oCACZ,KAAK,IAAI,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,oBAC1B,wPAAC,oJAAK;4CAAW,SAAQ;4CAAU,WAAU;sDAC1C;2CADS;;;;;oCAIb,KAAK,IAAI,CAAC,MAAM,GAAG,mBAClB,wPAAC,oJAAK;wCAAC,SAAQ;wCAAU,WAAU;;4CAAU;4CACzC,KAAK,IAAI,CAAC,MAAM,GAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASvC;uCAEe", "debugId": null}}, {"offset": {"line": 265, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Internship-Projects/The-Salty-Devs/client/src/app/page.tsx"], "sourcesContent": ["import React from 'react';\r\nimport { getAllPosts } from '@/lib/api';\r\nimport PostCard from '@/components/postCard';\r\n\r\nexport default async function Home() {\r\n  const { posts, totalCount } = await getAllPosts();\r\n  console.log('Total Posts fetched:', totalCount);\r\n\r\n  return (\r\n    <main className=\"container mx-auto px-4 py-8\">\r\n      <div className=\"text-center mb-12\">\r\n        <h1 className=\"text-4xl md:text-6xl font-bold mb-4\">\r\n          Welcome to Our Blog\r\n        </h1>\r\n        <p className=\"text-xl text-muted-foreground max-w-2xl mx-auto\">\r\n          Discover insights, tutorials, and thoughts on web development, design,\r\n          and technology.\r\n        </p>\r\n      </div>\r\n\r\n      <div className=\"grid gap-6 md:grid-cols-2 lg:grid-cols-3\">\r\n        {posts.map((post) => (\r\n          <PostCard key={post.id} post={post} />\r\n        ))}\r\n      </div>\r\n    </main>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;;AACA;AACA;;;;AAEe,eAAe;IAC5B,MAAM,EAAE,KAAK,EAAE,UAAU,EAAE,GAAG,MAAM,IAAA,0IAAW;IAC/C,QAAQ,GAAG,CAAC,wBAAwB;IAEpC,qBACE,wPAAC;QAAK,WAAU;;0BACd,wPAAC;gBAAI,WAAU;;kCACb,wPAAC;wBAAG,WAAU;kCAAsC;;;;;;kCAGpD,wPAAC;wBAAE,WAAU;kCAAkD;;;;;;;;;;;;0BAMjE,wPAAC;gBAAI,WAAU;0BACZ,MAAM,GAAG,CAAC,CAAC,qBACV,wPAAC,4JAAQ;wBAAe,MAAM;uBAAf,KAAK,EAAE;;;;;;;;;;;;;;;;AAKhC", "debugId": null}}]}